# Rewriting Halmos Parser Using AST Information

## Overview

This document outlines how to enhance the current Halmos log parser by leveraging Abstract Syntax Tree (AST) information that Halmos generates during compilation. The current parser relies on string parsing of logs, but we can make it more robust and accurate by using the structured AST data that Halmos already produces.

## Current Parser Limitations

The existing Halmos parser in `src/halmos/logParser.ts` has several limitations:

1. **String-based parsing**: Relies on regex and string matching to extract function calls and parameters
2. **Limited type information**: Cannot accurately determine parameter types from logs alone
3. **Fragile parameter mapping**: Uses heuristic matching that can fail with complex parameter names
4. **No function signature validation**: Cannot verify if extracted function calls match actual contract interfaces
5. **Missing context**: Lacks understanding of contract structure and inheritance

## How Halmos Uses AST

Based on the Halmos source code analysis, Halmos internally uses AST information for:

### 1. Contract Mapping (`src/halmos/mapper.py`)
- Maps function selectors to function names
- Tracks contract inheritance and structure
- Maintains bytecode to contract name mappings
- Stores function, event, and error signatures

### 2. Build Output Processing (`src/halmos/build.py`)
- Parses Solidity compilation artifacts
- Extracts AST nodes for each contract
- Creates mappings between source files and contracts
- Handles library linking and immutable references

### 3. AST Node Types Tracked
```python
SELECTOR_FIELDS = {
    "VariableDeclaration": "functionSelector",
    "FunctionDefinition": "functionSelector", 
    "EventDefinition": "eventSelector",
    "ErrorDefinition": "errorSelector",
}
```

## Proposed AST-Enhanced Parser Architecture

### 1. AST Data Integration

Create a new module `src/halmos/astParser.ts` that:

```typescript
interface HalmosASTData {
  contracts: Map<string, ContractInfo>;
  functions: Map<string, FunctionInfo>;
  events: Map<string, EventInfo>;
  errors: Map<string, ErrorInfo>;
}

interface ContractInfo {
  name: string;
  bytecode: string;
  functions: FunctionInfo[];
  inheritance: string[];
  sourceFile: string;
}

interface FunctionInfo {
  name: string;
  selector: string;
  parameters: ParameterInfo[];
  returnType: string;
  visibility: string;
  stateMutability: string;
}

interface ParameterInfo {
  name: string;
  type: string;
  indexed?: boolean; // for events
}
```

### 2. Enhanced Log Processing

Modify the log parser to use AST data:

```typescript
export class ASTEnhancedHalmosParser {
  private astData: HalmosASTData;
  
  constructor(buildArtifacts: any) {
    this.astData = this.parseAST(buildArtifacts);
  }
  
  parseCounterexample(logs: string): EnhancedPropertyAndSequence[] {
    // Use AST data to validate and enhance parsed information
  }
  
  private validateFunctionCall(
    functionName: string, 
    parameters: string[]
  ): ValidationResult {
    // Check against AST function definitions
  }
  
  private mapParameterTypes(
    functionSelector: string,
    parameterValues: Map<string, string>
  ): TypedParameter[] {
    // Use AST to determine correct parameter types
  }
}
```

### 3. Improved Function Generation

Enhance `functionGenerator.ts` with AST-aware generation:

```typescript
export const generateASTAwareTestFunction = (
  propSeq: EnhancedPropertyAndSequence,
  astData: HalmosASTData,
  identifier: string,
  index: number
): string => {
  // Use AST data to generate more accurate function calls
  const functionInfo = astData.functions.get(propSeq.functionSelector);
  
  if (functionInfo) {
    // Generate typed parameter declarations
    // Create accurate function calls with proper parameter mapping
    // Add type validation and casting where needed
  }
};
```

## Implementation Steps

### Phase 1: AST Data Extraction
1. Create AST parser that reads Foundry build artifacts
2. Extract contract definitions, function signatures, and parameter types
3. Build mapping structures for quick lookup during log parsing

### Phase 2: Enhanced Log Parser
1. Modify existing parser to use AST data for validation
2. Improve parameter type detection using function signatures
3. Add function selector validation against AST data

### Phase 3: Improved Function Generation
1. Generate more accurate Solidity test functions
2. Use proper type casting and parameter mapping
3. Add contract interface validation

### Phase 4: Error Handling and Validation
1. Add comprehensive error handling for AST mismatches
2. Provide fallback to current string-based parsing
3. Add validation warnings for potential issues

## Benefits of AST-Enhanced Parsing

### 1. Accuracy
- **Type Safety**: Proper parameter type mapping using contract interfaces
- **Validation**: Function calls validated against actual contract definitions
- **Completeness**: All function parameters properly identified and typed

### 2. Robustness
- **Less Fragile**: Reduced dependency on string parsing heuristics
- **Better Error Handling**: Clear identification of parsing failures
- **Fallback Support**: Graceful degradation to current parsing when AST unavailable

### 3. Enhanced Features
- **Contract Context**: Understanding of inheritance and contract relationships
- **Event Parsing**: Proper handling of events and their parameters
- **Library Support**: Better handling of library calls and linking

## Integration with Existing Codebase

### Backward Compatibility
- Maintain existing API for `halmosSequenceToFunction` and `halmosLogsToFunctions`
- Add new AST-enhanced functions as optional alternatives
- Provide migration path for existing users

### Configuration
```typescript
interface HalmosParserConfig {
  useAST: boolean;
  buildArtifactsPath?: string;
  fallbackToStringParsing: boolean;
  validateFunctionCalls: boolean;
}
```

### Error Handling
```typescript
enum ParsingMode {
  AST_ONLY,
  STRING_ONLY, 
  AST_WITH_FALLBACK
}
```

## Example Usage

```typescript
// Enhanced parser with AST support
const astParser = new ASTEnhancedHalmosParser(buildArtifacts);
const results = astParser.parseCounterexample(halmosLogs);

// Generate more accurate test functions
const testFunction = generateASTAwareTestFunction(
  results[0], 
  astParser.getASTData(), 
  "test_id", 
  0
);
```

## Next Steps

1. **Research Phase**: Analyze Foundry build artifacts structure
2. **Prototype**: Create minimal AST parser for function signatures
3. **Integration**: Modify existing parser to use AST data
4. **Testing**: Validate against existing test cases
5. **Documentation**: Update API documentation and examples

This AST-enhanced approach will make the Halmos parser more reliable, accurate, and maintainable while preserving backward compatibility with the existing string-based parsing approach.
