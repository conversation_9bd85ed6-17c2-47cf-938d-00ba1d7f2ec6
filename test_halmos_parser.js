const { halmosSequenceToFunction } = require("./lib/cjs/halmos/index.js");

// Test input based on the provided sample
const testSequence = `Counterexample:
    p_keys[0]_address_f8a6ab2_00 = 0x00
    p_keys[1]_address_4e1a7c6_00 = 0x00
    p_keys_length_36fa12a_00 = 0x02
    p_values_length_e4b6baa_00 = 0x02
[FAIL] check_parallel_arrays_consistency(address[],uint256[]) (paths: 14, time: 0.16s, bounds: [keys=[0, 1, 2], values=[0, 1, 2]])`;

const brokenProperty = "check_parallel_arrays_consistency(address[],uint256[])";
const identifier = "18";

console.log("Generated test function:");
console.log(
  halmosSequenceToFunction(testSequence, brokenProperty, identifier, 0)
);

console.log("\n" + "=".repeat(80));
console.log("Expected output should include:");
console.log("1. address[] memory keys_array = new address[](2);");
console.log("2. keys_array[0] = 0x0000000000000000000000000000000000000000;");
console.log("3. keys_array[1] = 0x0000000000000000000000000000000000000000;");
console.log("4. uint256[] memory values_array = new uint256[](2);");
console.log("5. check_parallel_arrays_consistency(keys_array, values_array);");
